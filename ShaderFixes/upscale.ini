[Present]
; Enables a custom shader that allows the stereo output mode to be
; upscaled. NOTE: uncomment only if 'upscaling' and resolution are not zero.
run = CustomShaderUpscale

;------------------------------------------------------------------------------------------------------
; The following custom shader can be used to perform upscaling to arbitrary resolutions,
; specifically useful for 4K 3D TVs or for DSR. To enable, uncomment the
; ";run = CustomShaderUpscale" in the Present section, set the custom resolution and
; enable "upscaling=1" in the Device section.
; If you uncomment upscale shader without actually enabling the upscaling in [Device]
; you might have wrong screen resolution or the game will not be able to enter the fullscreen mode.
; Both Upscaling and SBS can be used at the same time.

[Resource3DVisionUpscaleBackupTexture]
[CustomShaderUpscale]
; Load a custom vertex + pixel shader:
vs = upscale.hlsl
ps = upscale.hlsl
; Explicitly unbind other shader types for safety:
hs = null
ds = null
gs = null
; Disable the OM blend stage that could interfere with the shader:
blend = disable
; Disable front/back face culling so the vertices can be in any rotation:
cull = none
; enable sampler with the filter: anisotropic_filter, linear_filter, point_filter
sampler = anisotropic_filter
; Use a triangle strip topology so we only have to output four vertices:
topology = triangle_strip
; Clear all render + depth targets to avoid compatibility issues:
run = BuiltInCommandListUnbindAllRenderTargets
; Bind the real back buffer as a render target. set_viewport ensures that the
; view port is the size of the buffer so the draw call will work:
o0 = set_viewport r_bb
; Back up any textures that were in the ps-t100 slot. The CustomResource
; section will already back up a lot of state, including shaders, render
; targets, depth targets, UAVs, viewports, blend state, rasterizer state,
; primitive topology, etc. but it does not back up textures:
Resource3DVisionUpscaleBackupTexture = reference ps-t101
; Use faked bb to give the shader access to the back buffers of
; both eyes:
; f_bb is the faked swap chain that has the game resolution
ps-t101 = f_bb
; Some rare games (e.g. Onechanbara Z2) use MSAA back buffers, which cannot be
; directly used with the reverse stereo blit and must be resolved to non MSAA
; versions first. The symptoms will be a black screen after enabling this
; shader with F11. In that case, replace the above line with these two:
; upscaling with msaa swap chain is currently only avialable if upscale_mode = 1
; ps-t100 = resolve_msaa f_bb
; Draw four vertices. The vertex shader will construct coordinates to cover the
; full screen using the SV_VertexID semantic so we don't need vertex buffers:
draw = 4, 0
; Restore the original texture from the ps-t100 slot:
post ps-t101 = reference Resource3DVisionUpscaleBackupTexture
; Tell 3DMigoto that upscaling has been performed. Before this "bb" referred
; to "f_bb" and afterwards will refer to "r_bb" until the next frame. Makes it
; easier to re-order upscaling and other shaders. If this command is not found
; anywhere in the d3dx.ini or upscaling is disabled, "bb" will refer to "r_bb"
special = upscaling_switch_bb
