//
// Generated by Microsoft (R) HLSL Shader Compiler 10.1
//
//   using 3Dmigoto v1.3.16 on Mon Jan  9 03:07:56 2023
//
//
// Buffer Definitions:
//
// tbuffer texBones
// {
//
//   float4x3 bones[1024];              // Index:    0-3071           Components: 12288
//
// }
//
//
// Resource Bindings:
//
// Name                                 Type  Format         Dim Slot Elements
// ------------------------------ ---------- ------- ----------- ---- --------
// texBones                          tbuffer      NA          NA    0        1
//
//
//
// Input signature:
//
// Name                 Index   Mask Register SysValue  Format   Used
// -------------------- ----- ------ -------- -------- ------- ------
// POSITION                 0   xyz         0     NONE   float   xyz
// NORMAL                   0   xyz         1     NONE   float   xyz
// TANGENT                  0   xyzw        2     NONE   float   xyzw
// BLENDWEIGHT              0   xyzw        3     NONE   float   xyzw
// BLENDINDICES             0   xyzw        4     NONE     int   xyzw
//
//
// Output signature:
//
// Name                 Index   Mask Register SysValue  Format   Used
// -------------------- ----- ------ -------- -------- ------- ------
// POSITION                 0   xyz         0     NONE   float   xyz
// TEXCOORD                 0   xyz         1     NONE   float   xyz
// TEXCOORD                 1   xyzw        2     NONE   float   xyzw
//
/*vs_4_0
dcl_resource_buffer (mixed,mixed,mixed,mixed) t0
dcl_input v0.xyz
dcl_input v1.xyz
dcl_input v2.xyzw
dcl_input v3.xyzw
dcl_input v4.xyzw
dcl_output o0.xyz
dcl_output o1.xyz
dcl_output o2.xyzw
dcl_temps 6
imul null, r0.xyzw, v4.xyzw, l(3, 3, 3, 3)
ld r1.xyzw, r0.yyyy, t0.xyzw
mul r1.xyzw, r1.xyzw, v3.yyyy
ld r2.xyzw, r0.xxxx, t0.xyzw
mad r1.xyzw, v3.xxxx, r2.xyzw, r1.xyzw
ld r2.xyzw, r0.zzzz, t0.xyzw
ld r0.xyzw, r0.wwww, t0.xyzw
mad r1.xyzw, v3.zzzz, r2.xyzw, r1.xyzw
mad r0.xyzw, v3.wwww, r0.xyzw, r1.xyzw
mov r1.xyz, v0.xyzx
mov r1.w, l(1.000000)
dp4 o0.x, r1.xyzw, r0.xyzw
imad r2.xyzw, v4.xxyy, l(3, 3, 3, 3), l(2, 1, 2, 1)
ld r3.xyzw, r2.wwww, t0.xyzw
mul r3.xyzw, r3.xyzw, v3.yyyy
ld r4.xyzw, r2.yyyy, t0.xyzw
mad r3.xyzw, v3.xxxx, r4.xyzw, r3.xyzw
imad r4.xyzw, v4.zzww, l(3, 3, 3, 3), l(2, 1, 2, 1)
ld r5.xyzw, r4.yyyy, t0.xyzw
mad r3.xyzw, v3.zzzz, r5.xyzw, r3.xyzw
ld r5.xyzw, r4.wwww, t0.xyzw
mad r3.xyzw, v3.wwww, r5.xyzw, r3.xyzw
dp4 o0.y, r1.xyzw, r3.xyzw
ld r5.xyzw, r2.zzzz, t0.xyzw
ld r2.xyzw, r2.xxxx, t0.xyzw
mul r5.xyzw, r5.xyzw, v3.yyyy
mad r2.xyzw, v3.xxxx, r2.xyzw, r5.xyzw
ld r5.xyzw, r4.xxxx, t0.xyzw
ld r4.xyzw, r4.zzzz, t0.xyzw
mad r2.xyzw, v3.zzzz, r5.xyzw, r2.xyzw
mad r2.xyzw, v3.wwww, r4.xyzw, r2.xyzw
dp4 o0.z, r1.xyzw, r2.xyzw
dp3 o1.y, v1.xyzx, r3.xyzx
dp3 o2.y, v2.xyzx, r3.xyzx
dp3 o1.z, v1.xyzx, r2.xyzx
dp3 o2.z, v2.xyzx, r2.xyzx
dp3 o1.x, v1.xyzx, r0.xyzx
dp3 o2.x, v2.xyzx, r0.xyzx
mov o2.w, v2.w
ret
// Approximately 40 instruction slots used*/

///////////////////////////////// HLSL Code /////////////////////////////////
// // ---- Created with 3Dmigoto v1.3.16 on Mon Jan  9 03:07:56 2023
 Buffer<float4> t0 : register(t0);
 cbuffer cb10 : register(b10)
{
  float4 cb10[4096];
}
Buffer<float4> t10 : register(t10);
//
//
//
//
// // 3Dmigoto declarations
 #define cmp -
 Texture1D<float4> IniParams : register(t120);
 Texture2D<float4> StereoParams : register(t125);
 #define VGCOUNT IniParams[140].x
 #define FRAME IniParams[141].x
//
//
 void main(
   float3 v0 : POSITION0,
   float3 v1 : NORMAL0,
   float4 v2 : TANGENT0,
   float4 v3 : BLENDWEIGHT0,
   int4 v4 : BLENDINDICES0,
   out float3 o0 : POSITION0,
   out float4 o1 : TEXCOORD0,
   out float4 o2 : TEXCOORD1)
 {
   float4 r0,r1,r2,r3,r4,r5,r6;
   uint4 bitmask, uiDest;
   float4 fDest;

   if (VGCOUNT > 0){
      r6.xyzw = (int4)v4.xyzw + int4(VGCOUNT*int(FRAME),VGCOUNT*int(FRAME),VGCOUNT*int(FRAME),VGCOUNT*int(FRAME));
      r0.xyzw = (int4)r6.xyzw * int4(3,3,3,3);
      r1.xyzw = t10.Load(r0.y).xyzw;
   }
   else{
     r6.xyzw = (int4)v4.xyzw;
     r0.xyzw = (int4)v4.xyzw * int4(3,3,3,3);
     r1.xyzw = t0.Load(r0.y).xyzw;
   }
   
   r1.xyzw = v3.yyyy * r1.xyzw;
   if (VGCOUNT > 0){
      r2.xyzw = t10.Load(r0.x).xyzw;
   }
   else{
      r2.xyzw = t0.Load(r0.x).xyzw;
   }
   r1.xyzw = v3.xxxx * r2.xyzw + r1.xyzw;
   if (VGCOUNT > 1){
      r2.xyzw = t10.Load(r0.z).xyzw;
      r0.xyzw = t10.Load(r0.w).xyzw;
   }
   else{
      r2.xyzw = t0.Load(r0.z).xyzw;
      r0.xyzw = t0.Load(r0.w).xyzw;
   }
   r1.xyzw = v3.zzzz * r2.xyzw + r1.xyzw;
   r0.xyzw = v3.wwww * r0.xyzw + r1.xyzw;
   r1.xyz = v0.xyz;
   r1.w = 1;
   o0.x = dot(r1.xyzw, r0.xyzw);
   r2.xyzw = mad((int4)r6.xxyy, int4(3,3,3,3), int4(2,1,2,1));
   if (VGCOUNT > 0){
      r3.xyzw = t10.Load(r2.w).xyzw;
   }
   else{
      r3.xyzw = t0.Load(r2.w).xyzw;
   }
   r3.xyzw = v3.yyyy * r3.xyzw;
   if (VGCOUNT > 0){
      r4.xyzw = t10.Load(r2.y).xyzw;
   }
   else{
      r4.xyzw = t0.Load(r2.y).xyzw;
   }

   r3.xyzw = v3.xxxx * r4.xyzw + r3.xyzw;
   r4.xyzw = mad((int4)r6.zzww, int4(3,3,3,3), int4(2,1,2,1));
   if (VGCOUNT > 0){
      r5.xyzw = t10.Load(r4.y).xyzw;
   }
   else{
      r5.xyzw = t0.Load(r4.y).xyzw;
   }
   r3.xyzw = v3.zzzz * r5.xyzw + r3.xyzw;
   if (VGCOUNT > 0){
      r5.xyzw = t10.Load(r4.w).xyzw;
   }
   else{
      r5.xyzw = t0.Load(r4.w).xyzw;
   }
   r3.xyzw = v3.wwww * r5.xyzw + r3.xyzw;
   o0.y = dot(r1.xyzw, r3.xyzw);
   if (VGCOUNT > 0){
      r5.xyzw = t10.Load(r2.z).xyzw;
      r2.xyzw = t10.Load(r2.x).xyzw;
   }
   else{
      r5.xyzw = t0.Load(r2.z).xyzw;
      r2.xyzw = t0.Load(r2.x).xyzw;
   }
   r5.xyzw = v3.yyyy * r5.xyzw;
   r2.xyzw = v3.xxxx * r2.xyzw + r5.xyzw;
   if (VGCOUNT > 0){
      r5.xyzw = t10.Load(r4.x).xyzw;
      r4.xyzw = t10.Load(r4.z).xyzw;
   }
   else{
      r5.xyzw = t0.Load(r4.x).xyzw;
      r4.xyzw = t0.Load(r4.z).xyzw;
   }
   r2.xyzw = v3.zzzz * r5.xyzw + r2.xyzw;
   r2.xyzw = v3.wwww * r4.xyzw + r2.xyzw;
   o0.z = dot(r1.xyzw, r2.xyzw);
   o1.y = dot(v1.xyz, r3.xyz);
   o2.y = dot(v2.xyz, r3.xyz);
   o1.z = dot(v1.xyz, r2.xyz);
   o2.z = dot(v2.xyz, r2.xyz);
   o1.x = dot(v1.xyz, r0.xyz);
   o2.x = dot(v2.xyz, r0.xyz);
   o2.w = v2.w;
   return;
 }