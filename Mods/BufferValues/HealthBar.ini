; Original by SilentNightSound
; Updated by LeoMods to only call the command once per frame on active character <PERSON>, adding namespaces, as well as to fix the loading bar issue

namespace=global\HealthBar

[Constants]
global persist $health = -1
global $hpbarCount=0
global $total=100
global $isPartySwapLoadBar=0
global $auxt=0
global $HPActive=0

[Present]
post $auxt=$auxt+1
post $total=$hpbarCount
post $hpbarCount=0
post $isPartySwapLoadBar=0
post $HPActive=0

[ShaderOverrideGroundHealthPS]
hash = 000d2ce199e12697
allow_duplicate_hash=overrule
if $HPActive && ps-t0 == 34 && $isPartySwapLoadBar == 1
	$hpbarCount = $hpbarCount+1
	if $hpbarCount == $total && $auxt%15 == 0
		store = $health, ps-cb0, 33
	endif
endif

[ShaderOverrideMap]
hash = df48ebf15d4e5d80
$isPartySwapLoadBar=1

[TextureOverrideIcons]
hash = ef054414
filter_index = 34